# File generated from our OpenAPI spec by <PERSON>ainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Union, Iterable
from typing_extensions import Literal, Required, TypedDict

from .content_block import ContentBlock
from .text_block_param import Text<PERSON><PERSON><PERSON>aram
from .image_block_param import <PERSON><PERSON><PERSON><PERSON>aram
from .document_block_param import <PERSON>ument<PERSON><PERSON>Param
from .thinking_block_param import Thinking<PERSON><PERSON>Para<PERSON>
from .tool_use_block_param import ToolU<PERSON><PERSON>lockParam
from .tool_result_block_param import ToolResultBlockParam
from .search_result_block_param import SearchResultBlockParam
from .server_tool_use_block_param import ServerToolUseBlockParam
from .redacted_thinking_block_param import RedactedThinking<PERSON><PERSON>Param
from .web_search_tool_result_block_param import WebSearchToolResultBlockParam

__all__ = ["MessageParam"]


class MessageParam(TypedDict, total=False):
    content: Required[
        Union[
            str,
            Iterable[
                Union[
                    TextBlockParam,
                    ImageBlockParam,
                    DocumentBlockParam,
                    SearchResultBlockParam,
                    ThinkingBlockParam,
                    RedactedThinkingBlockParam,
                    ToolUseBlockParam,
                    ToolResultBlockParam,
                    ServerToolUseBlockParam,
                    WebSearchToolResultBlockParam,
                    ContentBlock,
                ]
            ],
        ]
    ]

    role: Required[Literal["user", "assistant"]]
