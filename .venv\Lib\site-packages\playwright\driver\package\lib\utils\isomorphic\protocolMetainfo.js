"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var protocolMetainfo_exports = {};
__export(protocolMetainfo_exports, {
  methodMetainfo: () => methodMetainfo
});
module.exports = __toCommonJS(protocolMetainfo_exports);
const methodMetainfo = /* @__PURE__ */ new Map([
  ["APIRequestContext.fetch", { title: '{method} "{url}"' }],
  ["APIRequestContext.fetchResponseBody", { title: "Get response body", group: "getter" }],
  ["APIRequestContext.fetchLog", { internal: true }],
  ["APIRequestContext.storageState", { title: "Get storage state" }],
  ["APIRequestContext.disposeAPIResponse", { internal: true }],
  ["APIRequestContext.dispose", { internal: true }],
  ["LocalUtils.zip", { internal: true }],
  ["LocalUtils.harOpen", { internal: true }],
  ["LocalUtils.harLookup", { internal: true }],
  ["LocalUtils.harClose", { internal: true }],
  ["LocalUtils.harUnzip", { internal: true }],
  ["LocalUtils.connect", { internal: true }],
  ["LocalUtils.tracingStarted", { internal: true }],
  ["LocalUtils.addStackToTracingNoReply", { internal: true }],
  ["LocalUtils.traceDiscarded", { internal: true }],
  ["LocalUtils.globToRegex", { internal: true }],
  ["Root.initialize", { internal: true }],
  ["Playwright.newRequest", { title: "Create request context" }],
  ["DebugController.initialize", { internal: true }],
  ["DebugController.setReportStateChanged", { internal: true }],
  ["DebugController.setRecorderMode", { internal: true }],
  ["DebugController.highlight", { internal: true }],
  ["DebugController.hideHighlight", { internal: true }],
  ["DebugController.resume", { internal: true }],
  ["DebugController.kill", { internal: true }],
  ["SocksSupport.socksConnected", { internal: true }],
  ["SocksSupport.socksFailed", { internal: true }],
  ["SocksSupport.socksData", { internal: true }],
  ["SocksSupport.socksError", { internal: true }],
  ["SocksSupport.socksEnd", { internal: true }],
  ["BrowserType.launch", { title: "Launch browser" }],
  ["BrowserType.launchPersistentContext", { title: "Launch persistent context" }],
  ["BrowserType.connectOverCDP", { title: "Connect over CDP" }],
  ["Browser.close", { title: "Close browser", pausesBeforeAction: true }],
  ["Browser.killForTests", { internal: true }],
  ["Browser.defaultUserAgentForTest", { internal: true }],
  ["Browser.newContext", { title: "Create context" }],
  ["Browser.newContextForReuse", { internal: true }],
  ["Browser.disconnectFromReusedContext", { internal: true }],
  ["Browser.newBrowserCDPSession", { title: "Create CDP session", group: "configuration" }],
  ["Browser.startTracing", { title: "Start browser tracing", group: "configuration" }],
  ["Browser.stopTracing", { title: "Stop browser tracing", group: "configuration" }],
  ["EventTarget.waitForEventInfo", { title: 'Wait for event "{info.event}"', snapshot: true }],
  ["BrowserContext.waitForEventInfo", { title: 'Wait for event "{info.event}"', snapshot: true }],
  ["Page.waitForEventInfo", { title: 'Wait for event "{info.event}"', snapshot: true }],
  ["WebSocket.waitForEventInfo", { title: 'Wait for event "{info.event}"', snapshot: true }],
  ["ElectronApplication.waitForEventInfo", { title: 'Wait for event "{info.event}"', snapshot: true }],
  ["AndroidDevice.waitForEventInfo", { title: 'Wait for event "{info.event}"', snapshot: true }],
  ["BrowserContext.addCookies", { title: "Add cookies", group: "configuration" }],
  ["BrowserContext.addInitScript", { title: "Add init script", group: "configuration" }],
  ["BrowserContext.clearCookies", { title: "Clear cookies", group: "configuration" }],
  ["BrowserContext.clearPermissions", { title: "Clear permissions", group: "configuration" }],
  ["BrowserContext.close", { title: "Close context", pausesBeforeAction: true }],
  ["BrowserContext.cookies", { title: "Get cookies", group: "getter" }],
  ["BrowserContext.exposeBinding", { title: "Expose binding", group: "configuration" }],
  ["BrowserContext.grantPermissions", { title: "Grant permissions", group: "configuration" }],
  ["BrowserContext.newPage", { title: "Create page" }],
  ["BrowserContext.registerSelectorEngine", { internal: true }],
  ["BrowserContext.setTestIdAttributeName", { internal: true }],
  ["BrowserContext.setExtraHTTPHeaders", { title: "Set extra HTTP headers", group: "configuration" }],
  ["BrowserContext.setGeolocation", { title: "Set geolocation", group: "configuration" }],
  ["BrowserContext.setHTTPCredentials", { title: "Set HTTP credentials", group: "configuration" }],
  ["BrowserContext.setNetworkInterceptionPatterns", { title: "Route requests", group: "route" }],
  ["BrowserContext.setWebSocketInterceptionPatterns", { title: "Route WebSockets", group: "route" }],
  ["BrowserContext.setOffline", { title: "Set offline mode" }],
  ["BrowserContext.storageState", { title: "Get storage state" }],
  ["BrowserContext.pause", { title: "Pause" }],
  ["BrowserContext.enableRecorder", { internal: true }],
  ["BrowserContext.disableRecorder", { internal: true }],
  ["BrowserContext.newCDPSession", { title: "Create CDP session", group: "configuration" }],
  ["BrowserContext.harStart", { internal: true }],
  ["BrowserContext.harExport", { internal: true }],
  ["BrowserContext.createTempFiles", { internal: true }],
  ["BrowserContext.updateSubscription", { internal: true }],
  ["BrowserContext.clockFastForward", { title: 'Fast forward clock "{ticksNumber|ticksString}"' }],
  ["BrowserContext.clockInstall", { title: 'Install clock "{timeNumber|timeString}"' }],
  ["BrowserContext.clockPauseAt", { title: 'Pause clock "{timeNumber|timeString}"' }],
  ["BrowserContext.clockResume", { title: "Resume clock" }],
  ["BrowserContext.clockRunFor", { title: 'Run clock "{ticksNumber|ticksString}"' }],
  ["BrowserContext.clockSetFixedTime", { title: 'Set fixed time "{timeNumber|timeString}"' }],
  ["BrowserContext.clockSetSystemTime", { title: 'Set system time "{timeNumber|timeString}"' }],
  ["Page.addInitScript", { title: "Add init script", group: "configuration" }],
  ["Page.close", { title: "Close page", pausesBeforeAction: true }],
  ["Page.emulateMedia", { title: "Emulate media", snapshot: true, pausesBeforeAction: true }],
  ["Page.exposeBinding", { title: "Expose binding", group: "configuration" }],
  ["Page.goBack", { title: "Go back", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Page.goForward", { title: "Go forward", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Page.requestGC", { title: "Request garbage collection", group: "configuration" }],
  ["Page.registerLocatorHandler", { title: "Register locator handler" }],
  ["Page.resolveLocatorHandlerNoReply", { internal: true }],
  ["Page.unregisterLocatorHandler", { title: "Unregister locator handler" }],
  ["Page.reload", { title: "Reload", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Page.expectScreenshot", { title: "Expect screenshot", snapshot: true, pausesBeforeAction: true }],
  ["Page.screenshot", { title: "Screenshot", snapshot: true, pausesBeforeAction: true }],
  ["Page.setExtraHTTPHeaders", { title: "Set extra HTTP headers", group: "configuration" }],
  ["Page.setNetworkInterceptionPatterns", { title: "Route requests", group: "route" }],
  ["Page.setWebSocketInterceptionPatterns", { title: "Route WebSockets", group: "route" }],
  ["Page.setViewportSize", { title: "Set viewport size", snapshot: true, pausesBeforeAction: true }],
  ["Page.keyboardDown", { title: 'Key down "{key}"', slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Page.keyboardUp", { title: 'Key up "{key}"', slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Page.keyboardInsertText", { title: 'Insert "{text}"', slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Page.keyboardType", { title: 'Type "{text}"', slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Page.keyboardPress", { title: 'Press "{key}"', slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Page.mouseMove", { title: "Mouse move", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Page.mouseDown", { title: "Mouse down", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Page.mouseUp", { title: "Mouse up", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Page.mouseClick", { title: "Click", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Page.mouseWheel", { title: "Mouse wheel", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Page.touchscreenTap", { title: "Tap", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Page.accessibilitySnapshot", { title: "Accessibility snapshot", group: "getter" }],
  ["Page.pdf", { title: "PDF" }],
  ["Page.snapshotForAI", { internal: true }],
  ["Page.startJSCoverage", { title: "Start JS coverage", group: "configuration" }],
  ["Page.stopJSCoverage", { title: "Stop JS coverage", group: "configuration" }],
  ["Page.startCSSCoverage", { title: "Start CSS coverage", group: "configuration" }],
  ["Page.stopCSSCoverage", { title: "Stop CSS coverage", group: "configuration" }],
  ["Page.bringToFront", { title: "Bring to front" }],
  ["Page.updateSubscription", { internal: true }],
  ["Frame.evalOnSelector", { title: "Evaluate", snapshot: true, pausesBeforeAction: true }],
  ["Frame.evalOnSelectorAll", { title: "Evaluate", snapshot: true, pausesBeforeAction: true }],
  ["Frame.addScriptTag", { title: "Add script tag", snapshot: true, pausesBeforeAction: true }],
  ["Frame.addStyleTag", { title: "Add style tag", snapshot: true, pausesBeforeAction: true }],
  ["Frame.ariaSnapshot", { title: "Aria snapshot", snapshot: true, pausesBeforeAction: true }],
  ["Frame.blur", { title: "Blur", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Frame.check", { title: "Check", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["Frame.click", { title: "Click", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["Frame.content", { title: "Get content", snapshot: true, pausesBeforeAction: true }],
  ["Frame.dragAndDrop", { title: "Drag and drop", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["Frame.dblclick", { title: "Double click", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["Frame.dispatchEvent", { title: 'Dispatch "{type}"', slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Frame.evaluateExpression", { title: "Evaluate", snapshot: true, pausesBeforeAction: true }],
  ["Frame.evaluateExpressionHandle", { title: "Evaluate", snapshot: true, pausesBeforeAction: true }],
  ["Frame.fill", { title: 'Fill "{value}"', slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["Frame.focus", { title: "Focus", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Frame.frameElement", { title: "Get frame element", group: "getter" }],
  ["Frame.resolveSelector", { internal: true }],
  ["Frame.highlight", { title: "Highlight element", group: "configuration" }],
  ["Frame.getAttribute", { title: 'Get attribute "{name}"', snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["Frame.goto", { title: 'Navigate to "{url}"', slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["Frame.hover", { title: "Hover", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["Frame.innerHTML", { title: "Get HTML", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["Frame.innerText", { title: "Get inner text", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["Frame.inputValue", { title: "Get input value", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["Frame.isChecked", { title: "Is checked", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["Frame.isDisabled", { title: "Is disabled", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["Frame.isEnabled", { title: "Is enabled", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["Frame.isHidden", { title: "Is hidden", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["Frame.isVisible", { title: "Is visible", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["Frame.isEditable", { title: "Is editable", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["Frame.press", { title: 'Press "{key}"', slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["Frame.querySelector", { title: "Query selector", snapshot: true }],
  ["Frame.querySelectorAll", { title: "Query selector all", snapshot: true }],
  ["Frame.queryCount", { title: "Query count", snapshot: true, pausesBeforeAction: true }],
  ["Frame.selectOption", { title: "Select option", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["Frame.setContent", { title: "Set content", snapshot: true, pausesBeforeAction: true }],
  ["Frame.setInputFiles", { title: "Set input files", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["Frame.tap", { title: "Tap", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["Frame.textContent", { title: "Get text content", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["Frame.title", { title: "Get page title", group: "getter" }],
  ["Frame.type", { title: 'Type "{text}"', slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["Frame.uncheck", { title: "Uncheck", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["Frame.waitForTimeout", { title: "Wait for timeout", snapshot: true }],
  ["Frame.waitForFunction", { title: "Wait for function", snapshot: true, pausesBeforeAction: true }],
  ["Frame.waitForSelector", { title: "Wait for selector", snapshot: true }],
  ["Frame.expect", { title: 'Expect "{expression}"', snapshot: true, pausesBeforeAction: true }],
  ["Worker.evaluateExpression", { title: "Evaluate" }],
  ["Worker.evaluateExpressionHandle", { title: "Evaluate" }],
  ["JSHandle.dispose", { internal: true }],
  ["ElementHandle.dispose", { internal: true }],
  ["JSHandle.evaluateExpression", { title: "Evaluate", snapshot: true, pausesBeforeAction: true }],
  ["ElementHandle.evaluateExpression", { title: "Evaluate", snapshot: true, pausesBeforeAction: true }],
  ["JSHandle.evaluateExpressionHandle", { title: "Evaluate", snapshot: true, pausesBeforeAction: true }],
  ["ElementHandle.evaluateExpressionHandle", { title: "Evaluate", snapshot: true, pausesBeforeAction: true }],
  ["JSHandle.getPropertyList", { title: "Get property list", group: "getter" }],
  ["ElementHandle.getPropertyList", { title: "Get property list", group: "getter" }],
  ["JSHandle.getProperty", { title: "Get JS property", group: "getter" }],
  ["ElementHandle.getProperty", { title: "Get JS property", group: "getter" }],
  ["JSHandle.jsonValue", { title: "Get JSON value", group: "getter" }],
  ["ElementHandle.jsonValue", { title: "Get JSON value", group: "getter" }],
  ["ElementHandle.evalOnSelector", { title: "Evaluate", snapshot: true, pausesBeforeAction: true }],
  ["ElementHandle.evalOnSelectorAll", { title: "Evaluate", snapshot: true, pausesBeforeAction: true }],
  ["ElementHandle.boundingBox", { title: "Get bounding box", snapshot: true, pausesBeforeAction: true }],
  ["ElementHandle.check", { title: "Check", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["ElementHandle.click", { title: "Click", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["ElementHandle.contentFrame", { title: "Get content frame", group: "getter" }],
  ["ElementHandle.dblclick", { title: "Double click", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["ElementHandle.dispatchEvent", { title: "Dispatch event", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["ElementHandle.fill", { title: 'Fill "{value}"', slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["ElementHandle.focus", { title: "Focus", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["ElementHandle.getAttribute", { title: "Get attribute", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["ElementHandle.hover", { title: "Hover", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["ElementHandle.innerHTML", { title: "Get HTML", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["ElementHandle.innerText", { title: "Get inner text", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["ElementHandle.inputValue", { title: "Get input value", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["ElementHandle.isChecked", { title: "Is checked", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["ElementHandle.isDisabled", { title: "Is disabled", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["ElementHandle.isEditable", { title: "Is editable", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["ElementHandle.isEnabled", { title: "Is enabled", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["ElementHandle.isHidden", { title: "Is hidden", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["ElementHandle.isVisible", { title: "Is visible", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["ElementHandle.ownerFrame", { title: "Get owner frame", group: "getter" }],
  ["ElementHandle.press", { title: 'Press "{key}"', slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["ElementHandle.querySelector", { title: "Query selector", snapshot: true }],
  ["ElementHandle.querySelectorAll", { title: "Query selector all", snapshot: true }],
  ["ElementHandle.screenshot", { title: "Screenshot", snapshot: true, pausesBeforeAction: true }],
  ["ElementHandle.scrollIntoViewIfNeeded", { title: "Scroll into view", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["ElementHandle.selectOption", { title: "Select option", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["ElementHandle.selectText", { title: "Select text", slowMo: true, snapshot: true, pausesBeforeAction: true }],
  ["ElementHandle.setInputFiles", { title: "Set input files", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["ElementHandle.tap", { title: "Tap", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["ElementHandle.textContent", { title: "Get text content", snapshot: true, pausesBeforeAction: true, group: "getter" }],
  ["ElementHandle.type", { title: "Type", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["ElementHandle.uncheck", { title: "Uncheck", slowMo: true, snapshot: true, pausesBeforeInput: true }],
  ["ElementHandle.waitForElementState", { title: "Wait for state", snapshot: true, pausesBeforeAction: true }],
  ["ElementHandle.waitForSelector", { title: "Wait for selector", snapshot: true }],
  ["Request.response", { internal: true }],
  ["Request.rawRequestHeaders", { internal: true }],
  ["Route.redirectNavigationRequest", { internal: true }],
  ["Route.abort", { title: "Abort request", group: "route" }],
  ["Route.continue", { title: "Continue request", group: "route" }],
  ["Route.fulfill", { title: "Fulfill request", group: "route" }],
  ["WebSocketRoute.connect", { title: "Connect WebSocket to server", group: "route" }],
  ["WebSocketRoute.ensureOpened", { internal: true }],
  ["WebSocketRoute.sendToPage", { title: "Send WebSocket message", group: "route" }],
  ["WebSocketRoute.sendToServer", { title: "Send WebSocket message", group: "route" }],
  ["WebSocketRoute.closePage", { internal: true }],
  ["WebSocketRoute.closeServer", { internal: true }],
  ["Response.body", { title: "Get response body", group: "getter" }],
  ["Response.securityDetails", { internal: true }],
  ["Response.serverAddr", { internal: true }],
  ["Response.rawResponseHeaders", { internal: true }],
  ["Response.sizes", { internal: true }],
  ["BindingCall.reject", { internal: true }],
  ["BindingCall.resolve", { internal: true }],
  ["Dialog.accept", { title: "Accept dialog" }],
  ["Dialog.dismiss", { title: "Dismiss dialog" }],
  ["Tracing.tracingStart", { title: "Start tracing", group: "configuration" }],
  ["Tracing.tracingStartChunk", { title: "Start tracing", group: "configuration" }],
  ["Tracing.tracingGroup", { title: 'Trace "{name}"' }],
  ["Tracing.tracingGroupEnd", { title: "Group end" }],
  ["Tracing.tracingStopChunk", { title: "Stop tracing", group: "configuration" }],
  ["Tracing.tracingStop", { title: "Stop tracing", group: "configuration" }],
  ["Artifact.pathAfterFinished", { internal: true }],
  ["Artifact.saveAs", { internal: true }],
  ["Artifact.saveAsStream", { internal: true }],
  ["Artifact.failure", { internal: true }],
  ["Artifact.stream", { internal: true }],
  ["Artifact.cancel", { internal: true }],
  ["Artifact.delete", { internal: true }],
  ["Stream.read", { internal: true }],
  ["Stream.close", { internal: true }],
  ["WritableStream.write", { internal: true }],
  ["WritableStream.close", { internal: true }],
  ["CDPSession.send", { title: "Send CDP command", group: "configuration" }],
  ["CDPSession.detach", { title: "Detach CDP session", group: "configuration" }],
  ["Electron.launch", { title: "Launch electron" }],
  ["ElectronApplication.browserWindow", { internal: true }],
  ["ElectronApplication.evaluateExpression", { title: "Evaluate" }],
  ["ElectronApplication.evaluateExpressionHandle", { title: "Evaluate" }],
  ["ElectronApplication.updateSubscription", { internal: true }],
  ["Android.devices", { internal: true }],
  ["AndroidSocket.write", { internal: true }],
  ["AndroidSocket.close", { internal: true }],
  ["AndroidDevice.wait", { title: "Wait" }],
  ["AndroidDevice.fill", { title: 'Fill "{text}"' }],
  ["AndroidDevice.tap", { title: "Tap" }],
  ["AndroidDevice.drag", { title: "Drag" }],
  ["AndroidDevice.fling", { title: "Fling" }],
  ["AndroidDevice.longTap", { title: "Long tap" }],
  ["AndroidDevice.pinchClose", { title: "Pinch close" }],
  ["AndroidDevice.pinchOpen", { title: "Pinch open" }],
  ["AndroidDevice.scroll", { title: "Scroll" }],
  ["AndroidDevice.swipe", { title: "Swipe" }],
  ["AndroidDevice.info", { internal: true }],
  ["AndroidDevice.screenshot", { title: "Screenshot" }],
  ["AndroidDevice.inputType", { title: "Type" }],
  ["AndroidDevice.inputPress", { title: "Press" }],
  ["AndroidDevice.inputTap", { title: "Tap" }],
  ["AndroidDevice.inputSwipe", { title: "Swipe" }],
  ["AndroidDevice.inputDrag", { title: "Drag" }],
  ["AndroidDevice.launchBrowser", { title: "Launch browser" }],
  ["AndroidDevice.open", { title: "Open app" }],
  ["AndroidDevice.shell", { title: "Execute shell command", group: "configuration" }],
  ["AndroidDevice.installApk", { title: "Install apk" }],
  ["AndroidDevice.push", { title: "Push" }],
  ["AndroidDevice.connectToWebView", { title: "Connect to Web View" }],
  ["AndroidDevice.close", { internal: true }],
  ["JsonPipe.send", { internal: true }],
  ["JsonPipe.close", { internal: true }]
]);
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  methodMetainfo
});
