# import os
# import tempfile
# import smtplib
# import asyncio
# import concurrent.futures
# from pathlib import Path
# from fastapi import FastAP<PERSON>, UploadFile, Form
# from fastapi.responses import J<PERSON>NResponse
# from pptx import Presentation
# from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
# from email.mime.text import MIMEText
# from typing import List
# from io import BytesIO
# from PIL import Image
# from dotenv import load_dotenv
# from openai import OpenAI
# try:
#     from playwright.async_api import async_playwright
#     PLAYWRIGHT_AVAILABLE = True
# except ImportError:
#     PLAYWRIGHT_AVAILABLE = False

# # Load .env file
# load_dotenv()

# # Initialize OpenAI client
# client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

# app = FastAPI()

# # TheyDo configuration
# theydo_base_url = "https://app.theydo.com"
# theydo_email = "<EMAIL>"
# theydo_password = "5xHqgWTwWivSTAy!"
# uploads_dir = Path(__file__).parent / "uploads"

# # ---------------------------
# # TheyDo Upload Function
# # ---------------------------
# def run_theydo_upload(files_dir: Path):
#     async def upload():
#         files = list(files_dir.iterdir())
#         if not files:
#             return False

#         async with async_playwright() as p:
#             browser = await p.chromium.launch(headless=False, args=["--start-maximized", "--disable-web-security"])
#             context = await browser.new_context(viewport={"width": 1920, "height": 1080})
#             page = await context.new_page()

#             # Login
#             await page.goto(f"{theydo_base_url}/login", wait_until="domcontentloaded", timeout=80000)
            
#             # Wait for and fill email
#             email_selectors = ['input[type="email"]', '[name="email"]', 'input[type="text"]', '#email']
#             for selector in email_selectors:
#                 try:
#                     await page.wait_for_selector(selector, timeout=5000)
#                     await page.fill(selector, theydo_email)
#                     break
#                 except:
#                     continue
            
#             await page.fill('input[type="password"]', theydo_password)
#             await page.click('button[type="submit"]')
#             await page.wait_for_url("**/workspace", timeout=60000)

#             # Change workspace to frontier-migration
#             await page.goto("https://app.theydo.com/radiant-digital/frontier-migration", wait_until="domcontentloaded", timeout=60000)
#             await asyncio.sleep(3)

#             # Go to Journey page
#             await page.goto("https://app.theydo.com/radiant-digital/frontier-migration/journey", wait_until="domcontentloaded", timeout=60000)
#             await asyncio.sleep(3)

#             # Click on +journey button on the right main page
#             journey_button_selectors = ['button:has-text("+journey")', 'text="+journey"', '[data-testid*="journey"]', 'button:has-text("+ Journey")']
#             for selector in journey_button_selectors:
#                 try:
#                     await page.wait_for_selector(selector, timeout=15000)
#                     await page.click(selector)
#                     break
#                 except:
#                     continue
#             await asyncio.sleep(3)

#             # Start a journey from a template
#             template_selectors = ['button:has-text("Start a journey from a template")', 'text="Start a journey from a template"', 'button:has-text("Start from template")', '[data-testid*="template"]']
#             for selector in template_selectors:
#                 try:
#                     await page.wait_for_selector(selector, timeout=15000)
#                     await page.click(selector)
#                     break
#                 except:
#                     continue
#             await asyncio.sleep(3)

#             # Select TheyDo templates
#             theydo_selectors = ['text=TheyDo templates', 'text="TheyDo templates"', '[data-testid*="theydo"]']
#             for selector in theydo_selectors:
#                 try:
#                     await page.wait_for_selector(selector, timeout=10000)
#                     await page.click(selector)
#                     break
#                 except:
#                     continue
#             await asyncio.sleep(3)

#             # Choose LO framework journey and continue
#             lo_selectors = ['text=LO framework', 'text="LO framework"', '[data-testid*="lo"]']
#             for selector in lo_selectors:
#                 try:
#                     await page.wait_for_selector(selector, timeout=10000)
#                     await page.click(selector)
#                     break
#                 except:
#                     continue
            
#             continue_selectors = ['button:has-text("Continue")', 'text="Continue"', '[data-testid*="continue"]']
#             for selector in continue_selectors:
#                 try:
#                     await page.wait_for_selector(selector, timeout=10000)
#                     await page.click(selector)
#                     break
#                 except:
#                     continue
#             await asyncio.sleep(10)

#             # Wait for journey creation to complete and navigate to data section
#             print("Waiting for journey to be created...")
#             await asyncio.sleep(5)
            
#             # Look for data/upload section in the created journey
#             print("Looking for data upload section...")
#             data_sections = [
#                 'text="Data"',
#                 'text="Upload"', 
#                 'text="Sources"',
#                 'button:has-text("Add data")',
#                 'button:has-text("Upload data")',
#                 '[data-testid*="data"]'
#             ]
            
#             data_found = False
#             for selector in data_sections:
#                 try:
#                     await page.wait_for_selector(selector, timeout=5000)
#                     await page.click(selector)
#                     data_found = True
#                     print(f"Found data section: {selector}")
#                     break
#                 except:
#                     continue
            
#             if data_found:
#                 await asyncio.sleep(3)
                
#                 # Now look for file upload
#                 print("Looking for file upload in data section...")
#                 file_uploaded = False
#                 file_selectors = [
#                     'input[type="file"]',
#                     'input[accept*="text"]',
#                     '[data-testid*="upload"]'
#                 ]
                
#                 for selector in file_selectors:
#                     try:
#                         file_input = await page.wait_for_selector(selector, timeout=10000)
#                         if file_input:
#                             file_paths = [str(f.resolve()) for f in files]
#                             await file_input.set_input_files(file_paths)
#                             file_uploaded = True
#                             print(f"Files uploaded: {selector}")
#                             break
#                     except:
#                         continue
                
#                 if not file_uploaded:
#                     print("No file upload found in data section")
#             else:
#                 print("Could not find data section - skipping file upload")
            
#             await asyncio.sleep(5)

#             # Click Assign type → Interview from dropdown
#             assign_selectors = ['button:has-text("Assign type")', 'text="Assign type"', '[data-testid*="assign"]']
#             for selector in assign_selectors:
#                 try:
#                     await page.wait_for_selector(selector, timeout=15000)
#                     await page.click(selector)
#                     break
#                 except:
#                     continue
            
#             interview_selectors = ['text=Interview', 'text="Interview"', '[data-testid*="interview"]']
#             for selector in interview_selectors:
#                 try:
#                     await page.wait_for_selector(selector, timeout=10000)
#                     await page.click(selector)
#                     break
#                 except:
#                     continue
            
#             # Click Save
#             save_selectors = ['button:has-text("Save")', 'text="Save"', '[data-testid*="save"]']
#             for selector in save_selectors:
#                 try:
#                     await page.wait_for_selector(selector, timeout=10000)
#                     await page.click(selector)
#                     break
#                 except:
#                     continue
#             await asyncio.sleep(3)

#             # Finally, click Extract quotes
#             extract_selectors = ['button:has-text("Extract quotes")', 'text="Extract quotes"', '[data-testid*="extract"]']
#             for selector in extract_selectors:
#                 try:
#                     await page.wait_for_selector(selector, timeout=15000)
#                     await page.click(selector)
#                     break
#                 except:
#                     continue

#             await asyncio.sleep(5)
#             await browser.close()
#             return True
    
#     try:
#         if os.name == 'nt':
#             asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
#         loop = asyncio.new_event_loop()
#         asyncio.set_event_loop(loop)
#         return loop.run_until_complete(upload())
#     except Exception as e:
#         print(f"TheyDo upload failed: {e}")
#         return False
#     finally:
#         loop.close()

# # ---------------------------
# # Extract text using OpenAI (vision or text refinement)
# # ---------------------------
# def extract_text_with_openai(text_or_image: bytes, is_image: bool = False) -> str:
#     try:
#         if is_image:
#             # Vision model (image input)
#             response = client.chat.completions.create(
#                 model="gpt-4o-mini",
#                 messages=[
#                     {"role": "system", "content": "Extract all visible text except white font with red background from this slide image accurately,and Create a detailed narrative report describing the data from the photo. Write it as a detailed process and work flow report but only in a richtext format."}
#                 ],
#                 modalities=["text", "image"],
#                 input=[
#                     {"role": "user", "content": [
#                         {"type": "input_text", "text": "Extract all text from this slide."},
#                         {"type": "input_image", "image_data": text_or_image}
#                     ]}
#                 ]
#             )
#             return response.choices[0].message.content
#         else:
#             # Refine plain extracted text
#             response = client.chat.completions.create(
#                 model="gpt-4o-mini",
#                 messages=[
#                     {"role": "system", "content": "Clean and structure the slide text."},
#                     {"role": "user", "content": text_or_image}
#                 ]
#             )
#             return response.choices[0].message.content
#     except Exception as e:
#         return f"[OpenAI Extraction Failed: {e}]"

# # ---------------------------
# # Extract text from PPT slides
# # ---------------------------
# def ppt_to_text_files(ppt_path: str, output_dir: str) -> List[str]:
#     prs = Presentation(ppt_path)
#     output_files = []

#     for i, slide in enumerate(prs.slides, start=1):
#         text_content = []

#         # Extract from text frames
#         for shape in slide.shapes:
#             if shape.has_text_frame:
#                 for paragraph in shape.text_frame.paragraphs:
#                     for run in paragraph.runs:
#                         if run.text.strip():
#                             text_content.append(run.text.strip())

#             # Extract from tables
#             if shape.has_table:
#                 for row in shape.table.rows:
#                     for cell in row.cells:
#                         if cell.text.strip():
#                             text_content.append(cell.text.strip())

#             # Extract from grouped shapes
#             if shape.shape_type == 6:  # group
#                 for shp in shape.shapes:
#                     if shp.has_text_frame:
#                         for paragraph in shp.text_frame.paragraphs:
#                             for run in paragraph.runs:
#                                 if run.text.strip():
#                                     text_content.append(run.text.strip())

#         slide_text = "\n".join(text_content).strip()

#         if slide_text:
#             refined_text = extract_text_with_openai(slide_text, is_image=False)
#         else:
#             # fallback: create blank image & try Vision (in real case export slide as PNG)
#             img = Image.new("RGB", (1280, 720), color="white")
#             buf = BytesIO()
#             img.save(buf, format="PNG")
#             buf.seek(0)
#             refined_text = extract_text_with_openai(buf.getvalue(), is_image=True)

#         narrative = f"Detailed Narrative Report - Slide {i}\n\nContent:\n{refined_text}\n"

#         file_path = os.path.join(output_dir, f"slide_{i}.txt")
#         with open(file_path, "w", encoding="utf-8") as f:
#             f.write(narrative)
#         output_files.append(file_path)

#     return output_files

# # ---------------------------
# # Send email with attachments via Outlook SMTP
# # ---------------------------
# def send_email_outlook(sender_email: str, password: str, receiver_email: str, files: List[str]):
#     smtp_server = "smtp.office365.com"
#     port = 587

#     msg = MIMEMultipart()
#     msg["From"] = sender_email
#     msg["To"] = receiver_email
#     msg["Subject"] = "TheyDo Narrative Reports"

#     body = "Please find attached TheyDo-compatible narrative reports from PPT."
#     msg.attach(MIMEText(body, "plain"))

#     # Save attachments to uploads folder
#     uploads_dir.mkdir(exist_ok=True)
#     for file in files:
#         with open(file, "r", encoding="utf-8") as f:
#             content = f.read()
#             attachment = MIMEText(content, "plain")
#             attachment.add_header("Content-Disposition", "attachment", filename=os.path.basename(file))
#             msg.attach(attachment)
            
#             # Save to uploads folder for TheyDo
#             upload_file_path = uploads_dir / os.path.basename(file)
#             with open(upload_file_path, "w", encoding="utf-8") as upload_file:
#                 upload_file.write(content)

#     with smtplib.SMTP(smtp_server, port) as server:
#         server.starttls()
#         server.login(sender_email, password)
#         server.sendmail(sender_email, receiver_email, msg.as_string())

# # ---------------------------
# # FastAPI Endpoint
# # ---------------------------
# @app.post("/convert_ppt/")
# async def convert_ppt(
#     file: UploadFile,
#     sender_email: str = Form(...),
#     password: str = Form(...),
#     receiver_email: str = Form(...)
# ):
#     try:
#         with tempfile.TemporaryDirectory() as tmpdir:
#             ppt_path = os.path.join(tmpdir, file.filename)
#             with open(ppt_path, "wb") as f:
#                 f.write(await file.read())

#             txt_files = ppt_to_text_files(ppt_path, tmpdir)

#             # Send email with attachments (also saves to uploads folder)
#             send_email_outlook(sender_email, password, receiver_email, txt_files)
            
#             # Try to upload to TheyDo
#             theydo_success = False
#             if PLAYWRIGHT_AVAILABLE:
#                 try:
#                     import concurrent.futures
#                     with concurrent.futures.ThreadPoolExecutor() as executor:
#                         future = executor.submit(run_theydo_upload, uploads_dir)
#                         theydo_success = future.result(timeout=3000)  # 5 minute timeout
#                 except Exception as e:
#                     print(f"TheyDo upload error: {e}")
#             else:
#                 print("Playwright not available - skipping TheyDo upload")
            
#             message = "PPT converted and emailed successfully."
#             if theydo_success:
#                 message = "PPT converted, emailed, and uploaded to TheyDo successfully."
#             else:
#                 message += " TheyDo upload failed - files saved to uploads folder."

#             return JSONResponse({
#                 "message": message,
#                 "files": [os.path.basename(f) for f in txt_files],
#                 "theydo_uploaded": theydo_success,
#                 "ai_provider": "openai"
#             })
#     except Exception as e:
#         return JSONResponse({"error": str(e)}, status_code=500)

import os
import tempfile
import smtplib
import asyncio
import concurrent.futures
from pathlib import Path
from fastapi import FastAPI, UploadFile, Form
from fastapi.responses import JSONResponse
from pptx import Presentation
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from typing import List
from io import BytesIO
from PIL import Image
from dotenv import load_dotenv
from openai import OpenAI
try:
    from playwright.async_api import async_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

# Load .env file
load_dotenv()

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

app = FastAPI()

# TheyDo configuration
theydo_base_url = "https://app.theydo.com"
theydo_email = "<EMAIL>"
theydo_password = "5xHqgWTwWivSTAy!"
uploads_dir = Path(__file__).parent / "uploads"

# ---------------------------
# TheyDo Upload Function
# ---------------------------
def run_theydo_upload(files_dir: Path):
    async def upload():
        files = list(files_dir.iterdir())
        if not files:
            print("ERROR: No files found in upload directory")
            return False

        print(f"INFO: Found {len(files)} files to upload: {[f.name for f in files]}")

        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=False, args=["--start-maximized", "--disable-web-security"])
            context = await browser.new_context(viewport={"width": 1920, "height": 1080})
            page = await context.new_page()

            try:
                # STEP 1: Login
                print("STEP 1: Starting login process...")
                await page.goto(f"{theydo_base_url}/login", wait_until="domcontentloaded", timeout=80000)
                print("INFO: Login page loaded successfully")

                # Fill email
                email_filled = False
                email_selectors = ['input[type="email"]', '[name="email"]', 'input[type="text"]', '#email']
                for selector in email_selectors:
                    try:
                        await page.wait_for_selector(selector, timeout=5000)
                        await page.fill(selector, theydo_email)
                        email_filled = True
                        print(f"INFO: Email filled using selector: {selector}")
                        break
                    except Exception as e:
                        print(f"WARNING: Email selector failed {selector}: {e}")
                        continue

                if not email_filled:
                    print("ERROR: Could not fill email field")
                    return False

                # Fill password and submit
                try:
                    await page.fill('input[type="password"]', theydo_password)
                    print("INFO: Password filled successfully")
                    await page.click('button[type="submit"]')
                    print("INFO: Login form submitted")
                    await page.wait_for_url("**/workspace", timeout=60000)
                    print("INFO: Login successful - redirected to workspace")
                except Exception as e:
                    print(f"ERROR: Login failed: {e}")
                    return False

                # STEP 2: Change workspace to frontier-migration
                print("STEP 2: Changing workspace to frontier-migration...")
                try:
                    await page.goto("https://app.theydo.com/radiant-digital/frontier-migration",
                                    wait_until="domcontentloaded", timeout=60000)
                    await asyncio.sleep(3)
                    print("INFO: Successfully navigated to frontier-migration workspace")
                except Exception as e:
                    print(f"ERROR: Failed to navigate to workspace: {e}")
                    return False

                # STEP 3: Go to Journey page
                print("STEP 3: Navigating to Journey page...")
                try:
                    await page.goto("https://app.theydo.com/radiant-digital/frontier-migration/journey",
                                    wait_until="domcontentloaded", timeout=60000)
                    await asyncio.sleep(3)
                    print("INFO: Successfully navigated to Journey page")
                except Exception as e:
                    print(f"ERROR: Failed to navigate to Journey page: {e}")
                    return False

                # STEP 4: Click the + Journey button on the right-hand side
                print("STEP 4: Looking for + Journey button on the right-hand side...")
                journey_button_clicked = False
                journey_button_selectors = [
                    'button:has-text("+ Journey")',
                    'button:has-text("+Journey")',
                    'button:has-text("Journey")',
                    'text="+ Journey"',
                    'text="+Journey"',
                    '[data-testid*="journey"]',
                    'button[aria-label*="Journey"]',
                    'button[class*="journey"]'
                ]

                for selector in journey_button_selectors:
                    try:
                        await page.wait_for_selector(selector, timeout=10000)
                        await page.click(selector)
                        journey_button_clicked = True
                        print(f"INFO: Successfully clicked + Journey button using selector: {selector}")
                        break
                    except Exception as e:
                        print(f"WARNING: Journey button selector failed {selector}: {e}")
                        continue

                if not journey_button_clicked:
                    print("ERROR: Could not find + Journey button")
                    await page.screenshot(path="debug_journey_button.png")
                    print("INFO: Screenshot saved as debug_journey_button.png")
                    return False

                await asyncio.sleep(5)
                print("INFO: + Journey button clicked, waiting for template selection modal...")

                # STEP 5: Verify we're on "Start a journey from a template" page
                print("STEP 5: Verifying we're on 'Start a journey from a template' page...")
                await asyncio.sleep(5)  # Wait for page to load after clicking + Journey

                # Check if we can see the "Start a journey from a template" headline
                template_page_indicators = [
                    'text="Start a journey from a template"',
                    'div:has-text("Start a journey from a template")',
                    'h1:has-text("Start a journey from a template")',
                    'h2:has-text("Start a journey from a template")',
                    'span:has-text("Start a journey from a template")'
                ]

                on_template_page = False
                for selector in template_page_indicators:
                    try:
                        await page.wait_for_selector(selector, timeout=10000)
                        on_template_page = True
                        print(f"INFO: Confirmed we're on template selection page, found headline: {selector}")
                        break
                    except Exception as e:
                        print(f"WARNING: Template page indicator failed {selector}: {e}")
                        continue

                if not on_template_page:
                    print("ERROR: Not on 'Start a journey from a template' page")
                    await page.screenshot(path="debug_template_page.png")
                    print("INFO: Screenshot saved as debug_template_page.png")
                    return False

                print("INFO: Successfully on template selection page, proceeding to TheyDo templates...")
                await asyncio.sleep(2)

                # STEP 6: Navigate to "TheyDo templates" tab
                print("STEP 6: Looking for and clicking 'TheyDo templates' tab...")
                theydo_templates_clicked = False

                # Look for TheyDo templates tab - it should be visible on the template selection page
                theydo_templates_selectors = [
                    'text="TheyDo templates"',
                    'button:has-text("TheyDo templates")',
                    'tab:has-text("TheyDo templates")',
                    '[role="tab"]:has-text("TheyDo templates")',
                    'div:has-text("TheyDo templates")',
                    'span:has-text("TheyDo templates")',
                    'a:has-text("TheyDo templates")',
                    '[data-testid*="theydo"]',
                    # Try more generic approaches
                    '[role="tab"]',
                    '.tab'
                ]

                for selector in theydo_templates_selectors:
                    try:
                        if selector in ['[role="tab"]', '.tab']:
                            # For generic tab selectors, check text content
                            elements = await page.query_selector_all(selector)
                            for element in elements:
                                text_content = await element.text_content()
                                if text_content and "TheyDo templates" in text_content:
                                    await element.click()
                                    theydo_templates_clicked = True
                                    print(f"INFO: Successfully clicked TheyDo templates tab using generic selector with text: {text_content}")
                                    break
                            if theydo_templates_clicked:
                                break
                        else:
                            await page.wait_for_selector(selector, timeout=15000)
                            await page.click(selector)
                            theydo_templates_clicked = True
                            print(f"INFO: Successfully clicked TheyDo templates tab using selector: {selector}")
                            break
                    except Exception as e:
                        print(f"WARNING: TheyDo templates selector failed {selector}: {e}")
                        continue

                if not theydo_templates_clicked:
                    print("ERROR: Could not find TheyDo templates tab")
                    await page.screenshot(path="debug_theydo_templates.png")
                    print("INFO: Screenshot saved as debug_theydo_templates.png")
                    return False

                print("INFO: Successfully clicked TheyDo templates tab, waiting for templates to load...")
                await asyncio.sleep(5)

                # STEP 7: Select "LO Framework Journey" template
                print("STEP 7: Looking for LO Framework Journey template...")
                lo_framework_selected = False

                # Look for the LO Framework Journey template in the TheyDo templates section
                lo_framework_selectors = [
                    'text="LO Framework Journey"',
                    'text="LO framework journey"',
                    'div:has-text("LO Framework Journey")',
                    'div:has-text("LO framework journey")',
                    'button:has-text("LO Framework Journey")',
                    'button:has-text("LO framework journey")',
                    'span:has-text("LO Framework")',
                    'text="LO framework"',
                    'img[alt*="LO Framework"]',
                    '[data-testid*="lo"]',
                    '[data-testid*="LO"]',
                    # Try template card selectors
                    '.template-card:has-text("LO Framework")',
                    '.template:has-text("LO Framework")',
                    '[class*="template"]:has-text("LO Framework")'
                ]

                for selector in lo_framework_selectors:
                    try:
                        await page.wait_for_selector(selector, timeout=15000)
                        await page.click(selector)
                        lo_framework_selected = True
                        print(f"INFO: Successfully selected LO Framework Journey template using selector: {selector}")
                        break
                    except Exception as e:
                        print(f"WARNING: LO Framework template selector failed {selector}: {e}")
                        continue

                if not lo_framework_selected:
                    print("ERROR: Could not find LO Framework Journey template")
                    await page.screenshot(path="debug_lo_framework.png")
                    print("INFO: Screenshot saved as debug_lo_framework.png")
                    return False

                print("INFO: LO Framework Journey template selected successfully")
                await asyncio.sleep(3)

                # STEP 8: Click Continue
                print("STEP 8: Looking for Continue button...")
                continue_clicked = False
                continue_selectors = [
                    'button:has-text("Continue")',
                    'text="Continue"',
                    'input[value="Continue"]',
                    'button[type="submit"]',
                    '[data-testid*="continue"]',
                    'button[aria-label*="continue"]',
                    'button:has-text("Next")',
                    'button:has-text("Create")',
                    'button:has-text("Proceed")'
                ]

                for selector in continue_selectors:
                    try:
                        await page.wait_for_selector(selector, timeout=15000)
                        await page.click(selector)
                        continue_clicked = True
                        print(f"INFO: Successfully clicked Continue button using selector: {selector}")
                        break
                    except Exception as e:
                        print(f"WARNING: Continue selector failed {selector}: {e}")
                        continue

                if not continue_clicked:
                    print("ERROR: Could not find Continue button")
                    await page.screenshot(path="debug_continue.png")
                    print("INFO: Screenshot saved as debug_continue.png")
                    return False

                await asyncio.sleep(10)
                print("INFO: Journey created successfully, proceeding to file upload...")

                # STEP 8: Look for upload sources section and upload files
                print("STEP 8: Looking for upload sources section...")

                # First, look for any upload-related buttons or sections
                upload_trigger_found = False
                upload_triggers = [
                    'button:has-text("Upload sources")',
                    'text="Upload sources"',
                    'button:has-text("Add data")',
                    'button:has-text("Upload data")',
                    'text="Data"',
                    'text="Sources"',
                    '[data-testid*="upload"]',
                    '[data-testid*="data"]',
                    'button:has-text("Import")'
                ]

                for selector in upload_triggers:
                    try:
                        await page.wait_for_selector(selector, timeout=5000)
                        await page.click(selector)
                        upload_trigger_found = True
                        print(f"INFO: Found upload trigger using selector: {selector}")
                        break
                    except Exception as e:
                        print(f"WARNING: Upload trigger selector failed {selector}: {e}")
                        continue

                if not upload_trigger_found:
                    print("WARNING: No upload trigger found, looking for direct file input...")

                await asyncio.sleep(3)

                # Look for the upload modal or file selection area
                print("INFO: Looking for file upload modal or area...")
                upload_modal_found = False
                upload_modal_selectors = [
                    'div:has-text("Upload sources")',
                    'div:has-text("Select files")',
                    'button:has-text("Select files")',
                    'text="Drag and drop"',
                    '.upload-modal',
                    '[role="dialog"]'
                ]

                for selector in upload_modal_selectors:
                    try:
                        await page.wait_for_selector(selector, timeout=5000)
                        upload_modal_found = True
                        print(f"INFO: Found upload modal using selector: {selector}")
                        break
                    except Exception as e:
                        print(f"WARNING: Upload modal selector failed {selector}: {e}")
                        continue

                # Now look for the actual file input or select files button
                print("INFO: Looking for file input or select files button...")
                file_uploaded = False
                file_selectors = [
                    'button:has-text("Select files")',
                    'input[type="file"]',
                    'input[accept*="text"]',
                    'input[accept*=".txt"]',
                    'input[accept*=".csv"]',
                    '[data-testid*="file"]',
                    '[data-testid*="upload"]'
                ]

                for selector in file_selectors:
                    try:
                        if 'input' in selector:
                            # For input elements, directly set files
                            file_input = await page.wait_for_selector(selector, timeout=10000)
                            if file_input:
                                file_paths = [str(f.resolve()) for f in files]
                                await file_input.set_input_files(file_paths)
                                file_uploaded = True
                                print(f"INFO: Files uploaded successfully using input selector: {selector}")
                                print(f"INFO: Uploaded files: {[f.name for f in files]}")
                                break
                        else:
                            # For button elements, click first then handle file dialog
                            await page.wait_for_selector(selector, timeout=10000)

                            # Set up file chooser handler before clicking
                            async def handle_file_chooser(file_chooser):
                                file_paths = [str(f.resolve()) for f in files]
                                await file_chooser.set_files(file_paths)
                                print(f"INFO: Files selected via file chooser: {[f.name for f in files]}")

                            page.on("filechooser", handle_file_chooser)
                            await page.click(selector)
                            file_uploaded = True
                            print(f"INFO: File selection triggered using button selector: {selector}")
                            break
                    except Exception as e:
                        print(f"WARNING: File upload selector failed {selector}: {e}")
                        continue

                if not file_uploaded:
                    print("ERROR: Could not upload files")
                    await page.screenshot(path="debug_file_upload.png")
                    print("INFO: Screenshot saved as debug_file_upload.png")
                    return False

                await asyncio.sleep(5)
                print("INFO: File upload completed, waiting for processing...")

                # STEP 9: Look for "Assign type to uploaded sources" section and select Interview
                print("STEP 9: Looking for Assign type section...")

                # Wait a bit for the upload to process and show the assign type section
                await asyncio.sleep(3)

                # Look for the assign type section or dropdown
                assign_found = False
                assign_selectors = [
                    'text="Assign type to uploaded sources"',
                    'div:has-text("Assign type")',
                    'button:has-text("Assign type")',
                    'select',
                    'dropdown',
                    '[data-testid*="assign"]',
                    '[data-testid*="type"]'
                ]

                for selector in assign_selectors:
                    try:
                        await page.wait_for_selector(selector, timeout=15000)
                        assign_found = True
                        print(f"INFO: Found assign type section using selector: {selector}")
                        break
                    except Exception as e:
                        print(f"WARNING: Assign type selector failed {selector}: {e}")
                        continue

                if not assign_found:
                    print("WARNING: Could not find assign type section, looking for Interview option directly...")

                # Look for Interview option in dropdown or selection area
                print("STEP 9b: Looking for Interview option...")
                interview_clicked = False
                interview_selectors = [
                    'text="Interview"',
                    'option:has-text("Interview")',
                    'button:has-text("Interview")',
                    'div:has-text("Interview")',
                    'select option[value*="interview"]',
                    '[data-testid*="interview"]',
                    'li:has-text("Interview")'
                ]

                for selector in interview_selectors:
                    try:
                        await page.wait_for_selector(selector, timeout=10000)
                        await page.click(selector)
                        interview_clicked = True
                        print(f"INFO: Successfully selected Interview using selector: {selector}")
                        break
                    except Exception as e:
                        print(f"WARNING: Interview selector failed {selector}: {e}")
                        continue

                if not interview_clicked:
                    print("ERROR: Could not find Interview option")
                    await page.screenshot(path="debug_interview_option.png")
                    print("INFO: Screenshot saved as debug_interview_option.png")
                    return False

                # STEP 10: Click Save button
                print("STEP 10: Looking for Save button...")
                save_clicked = False
                save_selectors = [
                    'button:has-text("Save")',
                    'text="Save"',
                    'button[type="submit"]',
                    '[data-testid*="save"]',
                    'button[aria-label*="save"]',
                    'input[type="submit"]',
                    'button:has-text("Apply")',
                    'button:has-text("Confirm")'
                ]

                for selector in save_selectors:
                    try:
                        await page.wait_for_selector(selector, timeout=10000)
                        await page.click(selector)
                        save_clicked = True
                        print(f"INFO: Successfully clicked Save using selector: {selector}")
                        break
                    except Exception as e:
                        print(f"WARNING: Save selector failed {selector}: {e}")
                        continue

                if not save_clicked:
                    print("ERROR: Could not find Save button")
                    await page.screenshot(path="debug_save_button.png")
                    print("INFO: Screenshot saved as debug_save_button.png")
                    return False

                await asyncio.sleep(5)
                print("INFO: Save completed, waiting for page to update...")

                # STEP 11: Finally, click Extract quotes
                print("STEP 11: Looking for Extract quotes button...")
                extract_clicked = False
                extract_selectors = [
                    'button:has-text("Extract quotes")',
                    'text="Extract quotes"',
                    'button:has-text("Extract")',
                    '[data-testid*="extract"]',
                    'button[aria-label*="extract"]',
                    'button:has-text("Generate quotes")',
                    'button:has-text("Process")'
                ]

                for selector in extract_selectors:
                    try:
                        await page.wait_for_selector(selector, timeout=15000)
                        await page.click(selector)
                        extract_clicked = True
                        print(f"INFO: Successfully clicked Extract quotes using selector: {selector}")
                        break
                    except Exception as e:
                        print(f"WARNING: Extract quotes selector failed {selector}: {e}")
                        continue

                if not extract_clicked:
                    print("ERROR: Could not find Extract quotes button")
                    await page.screenshot(path="debug_extract_quotes.png")
                    print("INFO: Screenshot saved as debug_extract_quotes.png")
                    return False

                await asyncio.sleep(10)
                print("SUCCESS: All automation steps completed successfully!")
                print("INFO: Extract quotes process initiated - check TheyDo for results")
                await browser.close()
                return True

            except Exception as e:
                print(f"CRITICAL ERROR: Automation failed: {e}")
                await page.screenshot(path="debug_critical_error.png")
                print("INFO: Critical error screenshot saved as debug_critical_error.png")
                await browser.close()
                return False

    try:
        if os.name == 'nt':
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        result = loop.run_until_complete(upload())
        print(f"INFO: TheyDo upload process completed with result: {result}")
        return result
    except Exception as e:
        print(f"CRITICAL ERROR: TheyDo upload process failed: {e}")
        return False
    finally:
        try:
            loop.close()
            print("INFO: Event loop closed successfully")
        except:
            print("WARNING: Failed to close event loop")

# ---------------------------
# Extract text using OpenAI
# ---------------------------
def extract_text_with_openai(text_or_image: bytes, is_image: bool = False) -> str:
    try:
        if is_image:
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "Extract all visible text and create a detailed workflow report."}
                ],
                modalities=["text", "image"],
                input=[
                    {"role": "user", "content": [
                        {"type": "input_text", "text": "Extract all text from this slide."},
                        {"type": "input_image", "image_data": text_or_image}
                    ]}
                ]
            )
            return response.choices[0].message.content
        else:
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {"role": "system", "content": "Clean and structure the slide text."},
                    {"role": "user", "content": text_or_image}
                ]
            )
            return response.choices[0].message.content
    except Exception as e:
        return f"[OpenAI Extraction Failed: {e}]"

# ---------------------------
# Extract text from PPT slides
# ---------------------------
def ppt_to_text_files(ppt_path: str, output_dir: str) -> List[str]:
    prs = Presentation(ppt_path)
    output_files = []

    for i, slide in enumerate(prs.slides, start=1):
        text_content = []
        for shape in slide.shapes:
            if shape.has_text_frame:
                for paragraph in shape.text_frame.paragraphs:
                    for run in paragraph.runs:
                        if run.text.strip():
                            text_content.append(run.text.strip())
            if shape.has_table:
                for row in shape.table.rows:
                    for cell in row.cells:
                        if cell.text.strip():
                            text_content.append(cell.text.strip())
            if shape.shape_type == 6:  # group
                for shp in shape.shapes:
                    if shp.has_text_frame:
                        for paragraph in shp.text_frame.paragraphs:
                            for run in paragraph.runs:
                                if run.text.strip():
                                    text_content.append(run.text.strip())

        slide_text = "\n".join(text_content).strip()

        if slide_text:
            refined_text = extract_text_with_openai(slide_text, is_image=False)
        else:
            img = Image.new("RGB", (1280, 720), color="white")
            buf = BytesIO()
            img.save(buf, format="PNG")
            buf.seek(0)
            refined_text = extract_text_with_openai(buf.getvalue(), is_image=True)

        narrative = f"Detailed Narrative Report - Slide {i}\n\nContent:\n{refined_text}\n"

        file_path = os.path.join(output_dir, f"slide_{i}.txt")
        with open(file_path, "w", encoding="utf-8") as f:
            f.write(narrative)
        output_files.append(file_path)

    return output_files

# ---------------------------
# Send email with attachments
# ---------------------------
def send_email_outlook(sender_email: str, password: str, receiver_email: str, files: List[str]):
    smtp_server = "smtp.office365.com"
    port = 587

    msg = MIMEMultipart()
    msg["From"] = sender_email
    msg["To"] = receiver_email
    msg["Subject"] = "TheyDo Narrative Reports"

    body = "Please find attached TheyDo-compatible narrative reports from PPT."
    msg.attach(MIMEText(body, "plain"))

    uploads_dir.mkdir(exist_ok=True)
    for file in files:
        with open(file, "r", encoding="utf-8") as f:
            content = f.read()
            attachment = MIMEText(content, "plain")
            attachment.add_header("Content-Disposition", "attachment", filename=os.path.basename(file))
            msg.attach(attachment)
            upload_file_path = uploads_dir / os.path.basename(file)
            with open(upload_file_path, "w", encoding="utf-8") as upload_file:
                upload_file.write(content)

    with smtplib.SMTP(smtp_server, port) as server:
        server.starttls()
        server.login(sender_email, password)
        server.sendmail(sender_email, receiver_email, msg.as_string())

# ---------------------------
# FastAPI Endpoint
# ---------------------------
@app.post("/convert_ppt/")
async def convert_ppt(
    file: UploadFile,
    sender_email: str = Form(...),
    password: str = Form(...),
    receiver_email: str = Form(...)
):
    print(f"INFO: Starting PPT conversion process for file: {file.filename}")
    try:
        with tempfile.TemporaryDirectory() as tmpdir:
            print(f"INFO: Created temporary directory: {tmpdir}")

            # Save uploaded file
            ppt_path = os.path.join(tmpdir, file.filename)
            with open(ppt_path, "wb") as f:
                f.write(await file.read())
            print(f"INFO: Saved uploaded file to: {ppt_path}")

            # Convert PPT to text files
            print("INFO: Starting PPT to text conversion...")
            txt_files = ppt_to_text_files(ppt_path, tmpdir)
            print(f"INFO: Created {len(txt_files)} text files: {[os.path.basename(f) for f in txt_files]}")

            # Send email
            print("INFO: Sending email with attachments...")
            try:
                send_email_outlook(sender_email, password, receiver_email, txt_files)
                print("INFO: Email sent successfully")
            except Exception as e:
                print(f"ERROR: Email sending failed: {e}")
                return JSONResponse({"error": f"Email sending failed: {str(e)}"}, status_code=500)

            # Try TheyDo upload
            theydo_success = False
            if PLAYWRIGHT_AVAILABLE:
                print("INFO: Playwright available - attempting TheyDo upload...")
                try:
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        print("INFO: Starting TheyDo upload in thread executor...")
                        future = executor.submit(run_theydo_upload, uploads_dir)
                        theydo_success = future.result(timeout=3000)  # 50 minute timeout
                        print(f"INFO: TheyDo upload completed with result: {theydo_success}")
                except concurrent.futures.TimeoutError:
                    print("ERROR: TheyDo upload timed out after 50 minutes")
                except Exception as e:
                    print(f"ERROR: TheyDo upload error: {e}")
            else:
                print("WARNING: Playwright not available - skipping TheyDo upload")

            # Prepare response
            if theydo_success:
                message = "SUCCESS: PPT converted, emailed, and uploaded to TheyDo successfully. All automation steps completed."
            else:
                message = "PARTIAL SUCCESS: PPT converted and emailed successfully. TheyDo upload failed - files saved to uploads folder for manual upload."

            print(f"INFO: Process completed. Final message: {message}")

            return JSONResponse({
                "message": message,
                "files": [os.path.basename(f) for f in txt_files],
                "theydo_uploaded": theydo_success,
                "ai_provider": "openai",
                "upload_directory": str(uploads_dir),
                "debug_info": "Check console logs for detailed step-by-step automation progress"
            })

    except Exception as e:
        error_msg = f"CRITICAL ERROR: PPT conversion process failed: {str(e)}"
        print(error_msg)
        return JSONResponse({"error": error_msg}, status_code=500)


