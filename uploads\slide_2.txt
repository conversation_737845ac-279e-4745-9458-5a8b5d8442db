Detailed Narrative Report - Slide 2

Content:
## Data Order Provisioning Process

**Overview**  
This document outlines the process flow of data order provisioning. Note that it is not a planning document but rather a preliminary analysis of various stages in the provisioning workflow.

### Process Flow Steps

1. **Order Validation**  
   - **Validation Methods:** XML, Service Compatibility  
   - **Systems Used:** iVAPP  
   - **Type:** Auto

2. **Order Provisioning**

3. **Dispatch**
   - **Order Dispatch System:** Optix Dispatch  
   - **Type:** Auto

4. **Activation**
   - **Activation Correction Systems:** iVAPP, Delphi  
   - **Type:** Manual/Auto depending on need

5. **Order Completion & Billing**
   - **Completion Systems:** iVAPP, SSP  
   - **Type:** Auto  
   - **Billing Systems:** SSP, Vision  
   - **Type:** Auto
  
6. **Facility Assignment**
   - **System:** iVAPP  
   - **Type:** Auto

7. **IP Availability Check** 
   - **Systems Used:** iVAPP, INAM  
   - **Type:** Auto

8. **Pre-Provisioning for Router, ONT & Testing**  
   - **Systems Used:** VNM, Delphi  
   - **Type:** Auto

9. **Facility Correction**  
   - **System:** iVAPP, Field Work  
   - **Type:** Manual

10. **Technician Assignment**  
    - **System:** Optix Dispatch  
    - **Type:** Auto

11. **Field Activity**  
    - **Tasks:** Drop placement and inside wiring  
    - **Systems Used:** Omega  
    - **Personnel:** Field Technician

12. **ONT Activation**  
    - **Systems Used:** Omega, Delphi, iVAPP, VNM  
    - **Personnel:** Field Technician / Auto

13. **Router Activation**  
    - **Systems Used:** Omega, HDM, HNM, CPEM  
    - **Personnel:** Field Technician / Auto

14. **Upsell (Sell One More)**  
    - **Systems Used:** Omega, CRMM, SSP, iVAPP  
    - **Personnel:** Field Technician  

15. **Dispatch Completion**  
    - **Systems Used:** Omega, Optix Dispatch, iVAPP  
    - **Personnel:** Field Technician  

16. **Order Correction**  
    - **System:** SSP  
    - **Personnel:** SFC, SSP OPS  

17. **Jeopardy Handling**  
    - **Systems Used:** iVAPP, Omega  
    - **Personnel:** DRC  

18. **Billing Correction**  
    - **System:** Vision  
    - **Personnel:** SFC, SSP OPS, Revenue Assurance  

### Statistics Overview
- **Technician Enroute:** 3%
- **Customer Notification:** 2%
  
### Additional Notes
- Order provisioning includes process interruptions and corrections depending on specific scenarios.
- Manual intervention is required in some steps for facility correction and technician assignment.  
- Monitoring of IP availability and pre-provisioning ensures readiness for installation.

This structured approach aims to provide clarity and efficiency in the data order provisioning process.
