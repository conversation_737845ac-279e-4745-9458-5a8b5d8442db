Detailed Narrative Report - Slide 2

Content:
# Data Order Provisioning Workflow

## Overview
- **Note:** This document outlines the provisioning workflow and is not intended as a planning document.

---

## Workflow Steps

1. **Order Validation**
   - Tools: iVAPP
   - Processes: XML validation, Service Compatibility

2. **Order Provisioning**
   - Automated tasks related to facility and resource assignment.
   - Tools: iVAPP, INAM 
     - Activities: 
       - IP availability check
       - Augmentation for Static and Dynamic

3. **Pre-Provisioning**
   - Focus: Router, ONT, and Testing 
   - Tools: VNM, Delphi 

4. **Order Completion**
   - Tool: iVAPP, SSP 
   - Activities: 
     - Finalization of provisioning tasks

5. **Facility Correction**
   - Tool: iVAPP, Field Work 
   - Process: Manual intervention for corrections

6. **Activation**
   - Tools: iVAPP, Delphi 
   - Activities: 
     - Activation Corrections 

7. **Provisioning Correction**
   - Tools: iVAPP, FSC, ITCC 

8. **Order Dispatch**
   - Tool: Optix Dispatch 

9. **Technician Assignment**
   - Tool: Optix Dispatch 

10. **Field Activity**
    - Activities: Drop placement and inside wiring 
    - Tool: Omega

11. **Router Activation**
    - Tools: Omega, HDM, HNM, CPEM 

12. **ONT Activation**
    - Tools: Omega, Delphi, iVAPP, VNM 

13. **Sell One More (Upsell)**
    - Tools: Omega, CRMM, SSP, iVAPP 

14. **Dispatch Completion**
    - Tools: Omega, Optix Dispatch 

15. **Billing**
    - Tools: SSP, Vision 

16. **Order Correction**
    - Tool: SSP 
    - Involvement: SFC, SSP OPS

17. **Jeopardy Handle**
    - Tools: iVAPP, Omega 

18. **Billing Correction**
    - Tool: Vision 
    - Involvement: SFC, SSP OPS, Revenue Assurance 

---

## Performance Metrics
- **Order Completion Rate:** 98%
- **Activation Success Rate:** 97%
- **Customer Satisfaction Score:** 99%

---

## Notifications
- **Technician Enroute Updates**
  - Tool: Omega 
- **Customer Notifications**
  - Tool: Contact Engine 

---

## Special Processes
- **Router Shipment for Self Install**
  - Tools: iVAPP, CPEM 

---

## Notes
- The percentages represent various key performance indicators (KPIs) throughout the workflow, indicating efficiency and success rates.
- Manual interventions are indicated where applicable to ensure flexibility and customer satisfaction.
