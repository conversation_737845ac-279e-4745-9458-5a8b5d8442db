Detailed Narrative Report - Slide 2

Content:
# Data Order Provisioning Process

## Overview
**Note:** This document outlines preliminary work and analysis; it does not serve as a planning document.

---

## Key Stages of Order Provisioning

1. **Order Validation**
   - XML and Service Compatibility Checks
   - **Tools:** iVAPP
   
2. **Order Provisioning**
   - Facility Assignment 
   - IP Availability Check (for Static and Dynamic)
   - **Tools:** iVAPP, INAM

3. **Pre-Provisioning**
   - For Router, ONT, and Testing
   - **Tools:** VNM, Delphi

4. **Order Completion**
   - Facility Correction and Manual Tech Assignment (if customer is unreachable)
   - **Tools:** iVAPP, SSP, OSP, ITCC, Optix Dispatch

5. **Activation Phase**
   - **Order Dispatch:** Optix Dispatch
   - **Technician Assignment:** Optix Dispatch 
   - **Field Activities:** Drop placement, inside wiring
   - **Tools:** Omega, Field Technicians

6. **Activation**
   - **ONT Activation**
   - **Router Activation**
   - **Upselling (Selling One More)**
   - **Tools:** Omega, Delphi, iVAPP, VNM, HDM, HNM, CPEM

7. **Dispatch Completion**
   - **Tools:** Omega, Optix Dispatch, iVAPP

8. **Billing**
   - **Tools:** SSP, Vision
   - Auto and Manual corrections as necessary
   - **Billing Correction:** Vision, Revenue Assurance

---

## Notifications and Customer Engagement
- **Technician Enroute Notifications:** Auto via Omega
- **Customer Notifications:** Contact Engine, Auto
- **Jeopardy Handling:** iVAPP, Omega

---

## Correction Mechanisms
- **Order Correction:** SSP
- **Billing Corrections:** Vision, with support from SFC and SSP OPS

---

## Performance Metrics
- Completion Rates by Process Stage:
  - 98% Customer Notification Effectiveness
  - 99% Order Validations
  - 90% Timely Dispatch and Activations
  - 85% Upsell Success Rate

---

### Conclusion
This structured process aims to ensure a seamless order provisioning experience, leveraging automation where possible while maintaining the flexibility for manual interventions when required.
