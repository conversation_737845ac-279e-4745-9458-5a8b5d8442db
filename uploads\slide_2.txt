Detailed Narrative Report - Slide 2

Content:
### Data Order Provisioning Workflow

---

#### Overview
- Preliminary Work & Analysis: Not a planning document
- Workflow Steps:
  1. Order Validation
  2. Order Provisioning
  3. Dispatch
  4. Activation
  5. Order Completion & Billing

---

#### Detailed Process Steps

1. **Order Validation**
   - Components: XML, Service Compatibility
   - Tools: iVAPP
   - Type: Auto

2. **Order Provisioning**
   - **Facility Assignment**
     - Tools: iVAPP
     - Type: Auto
   - **IP Availability Check**
     - Augmentation for Static and Dynamic
     - Tools: iVAPP, INAM
     - Type: Auto
   - **Pre-Provisioning**
     - For Router, ONT & Testing
     - Tools: VNM, Delphi
     - Type: Auto

3. **Order Completion**
   - Tools: iVAPP, SSP
   - Type: Auto

4. **Facility Correction**
   - Tools: iVAPP, Field Work
   - Type: Manual

5. **Technician Assignment**
   - Tools: Optix Dispatch
   - Type: Auto

6. **Field Activity**
   - Drop Placement and Inside Wiring
   - Tools: Omega
   - Field Technician Involvement

7. **Activation**
   - **ONT Activation**
     - Tools: Omega, Delphi, iVAPP, VNM
     - Field Technician / Auto
   - **Router Activation**
     - Tools: Omega, HDM, HNM, CPEM
     - Field Technician / Auto

8. **Upsell Opportunity**
   - Sell One More
   - Tools: Omega, CRMM, SSP, iVAPP
   - Responsible: Field Technician

9. **Dispatch Completion**
   - Tools: Omega, Optix Dispatch, iVAPP
   - Responsible: Field Technician

---

#### Billing
- **Billing Completion**
  - Tools: SSP, Vision
  - Type: Auto
- **Order Correction**
  - Tools: SSP
  - Components: SFC, SSP OPS
- **Jeopardy Handling**
  - Tools: iVAPP, Omega
  - Responsible: DRC
- **Billing Correction**
  - Tools: Vision
  - Components: SFC, SSP OPS, Revenue Assurance

---

#### Metrics Overview
- **Technician Enroute:** 98%
- **Customer Notification:** 97%
- **Router Shipment for Self-Install:** 99%
  
#### Manual and Automated Approval Rates
- **Manual:** 85%
- **Auto:** 97%, 90%, 98%, 99%

--- 

This structured overview of the Data Order Provisioning workflow outlines the key steps, involved tools, and various automation and manual processes relevant to order handling, dispatch, and billing.
